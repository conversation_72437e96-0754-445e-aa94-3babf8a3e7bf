import React from 'react';
import {
  Box,
  Typography,
  ListItem,
  ListItemText,
  Chip,
  Tooltip,
  LinearProgress
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Assignment as AssignmentIcon,
  Class as ClassIcon,
  Group as GroupIcon,
  Flag as FlagIcon,
  Launch as LaunchIcon
} from '@mui/icons-material';

const ClassificationItem = ({ classification, categoryId, onClick }) => {
  // Helper function to format classification name with phase
  const formatClassificationName = (classification) => {
    const baseName = classification.name;
    if (classification.phase) {
      return `${baseName} - Phase ${classification.phase}`;
    }
    return baseName;
  };

  return (
    <ListItem
      sx={{
        mb: 2,
        p: 2,
        borderRadius: 2,
        backgroundColor: 'rgba(59, 140, 110, 0.05)',
        cursor: 'pointer',
        transition: 'all 0.2s',
        '&:hover': {
          backgroundColor: 'rgba(59, 140, 110, 0.1)',
          transform: 'translateY(-2px)',
          boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
        }
      }}
      onClick={() => onClick(classification, categoryId)}>
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="subtitle2" sx={{ mr: 1 }}>
                {formatClassificationName(classification)}
              </Typography>
              <Tooltip title="Click to view groups in this classification">
                <Box
                  component="span"
                  sx={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    color: '#3b8c6e',
                    fontSize: '0.75rem',
                    fontWeight: 'bold'
                  }}
                >
                  <LaunchIcon sx={{ fontSize: '0.875rem', mr: 0.5 }} />
                  View Groups
                </Box>
              </Tooltip>
            </Box>
            <Tooltip title={`Phase ${classification.phase}`}>
              <Chip
                icon={<FlagIcon />}
                label={`Phase: ${classification.phase}`}
                size="small"
                sx={{
                  backgroundColor: classification.phase === 2 ? '#ff9800' : '#3b8c6e',
                  color: 'white',
                  '& .MuiChip-icon': { color: 'white' }
                }}
              />
            </Tooltip>
          </Box>
        }
        secondary={
          <Box sx={{ mt: 2 }}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
              <Chip
                icon={<GroupIcon />}
                label={`${classification.totalGroups} Groups`}
                size="small"
                variant="outlined"
                sx={{ borderColor: '#3b8c6e', color: '#3b8c6e' }}
              />
              {classification.isFullyScored ? (
                <Chip
                  icon={<CheckCircleIcon />}
                  label="Fully Scored"
                  size="small"
                  sx={{
                    backgroundColor: '#3b8c6e',
                    color: 'white',
                    '& .MuiChip-icon': { color: 'white' }
                  }}
                />
              ) : classification.scoredGroups === 0 ? (
                <Chip
                  icon={<AssignmentIcon />}
                  label="Not Started"
                  size="small"
                  sx={{
                    backgroundColor: '#ff9800',
                    color: 'white',
                    '& .MuiChip-icon': { color: 'white' }
                  }}
                />
              ) : (
                <>
                  <Chip
                    icon={<CheckCircleIcon />}
                    label={`${classification.scoredGroups} Scored`}
                    size="small"
                    variant="outlined"
                    sx={{ borderColor: '#3b8c6e', color: '#3b8c6e' }}
                  />
                  <Chip
                    icon={<AssignmentIcon />}
                    label={`${classification.unscoredGroups} Remaining`}
                    size="small"
                    variant="outlined"
                    sx={{ borderColor: '#ff9800', color: '#ff9800' }}
                  />
                </>
              )}
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="body2" sx={{ mr: 1, minWidth: '100px' }}>
                Group Scoring:
              </Typography>
              <Box sx={{ flexGrow: 1 }}>
                <LinearProgress
                  variant="determinate"
                  value={classification.progress}
                  sx={{
                    height: 6,
                    borderRadius: 5,
                    backgroundColor: 'rgba(59, 140, 110, 0.1)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: classification.isFullyScored
                        ? '#3b8c6e'
                        : (classification.scoredGroups === 0 ? '#ff9800' : '#ffc107'),
                    }
                  }}
                />
              </Box>
              <Typography variant="body2" sx={{ ml: 1, fontWeight: 'bold' }}>
                {Math.round(classification.progress)}%
              </Typography>
            </Box>
          </Box>
        }
      />
    </ListItem>
  );
};

export default ClassificationItem;
